# Development Environment Configuration for Makos

# =============================================================================
# ORACLE DATABASE CONFIGURATION for development
# =============================================================================
spring.datasource.oracle.url=***********************************
spring.datasource.oracle.driverClassName=oracle.jdbc.OracleDriver
spring.datasource.oracle.username=iym
spring.datasource.oracle.password=iym

# Oracle HikariCP Configuration for development
spring.datasource.oracle.hikari.connection-timeout=20000
spring.datasource.oracle.hikari.maximum-pool-size=10
spring.datasource.oracle.hikari.minimum-idle=5
spring.datasource.oracle.hikari.idle-timeout=300000
spring.datasource.oracle.hikari.max-lifetime=1200000
spring.datasource.oracle.hikari.leak-detection-threshold=60000
spring.datasource.oracle.hikari.pool-name=OracleHikariPool

spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
spring.jpa.properties.hibernate.default_schema=iym

# JPA configuration for development
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.properties.hibernate.hbm2ddl.auto=validate

# Development specific logging
logging.level.root=INFO
logging.level.iym=DEBUG
logging.level.org.hibernate=ERROR
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true



# Application specific properties for development
app.init-db=true

# CORS Configuration for development (specific local origins)
cors.allowed.origins=http://localhost:3000,http://localhost:4000,http://127.0.0.1:3000,http://127.0.0.1:4000,http://localhost:8080
